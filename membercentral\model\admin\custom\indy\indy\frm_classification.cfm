﻿<cfsavecontent variable="local.classificationJS">
	<cfoutput>
	<script language="javascript">
		function startsWithAlpha(myString){
			var c = myString.charAt(0);
			if( isNaN(c) ) return true;
			else return false;
		}
		function validateClassification() {
			var xName = $('##nameOverride').val();
			var sfs = $('##groupSetID').val();
			mca_hideAlert('classification_err');

			var arrReq = new Array();
			if (xName.length > 0) {
				if(!mca_isAlphaNumeric(xName)){ arrReq[arrReq.length] = 'The Classification Name Override can only contain letters and numbers.'; }
				if(!startsWithAlpha(xName)){ arrReq[arrReq.length] = 'The Classification Name Override must start with a letter.'; }
			}
			if (sfs == '') { arrReq[arrReq.length] = 'Choose a Group Set for this classification.'; }

			if (arrReq.length > 0) {
				mca_showAlert('classification_err', arrReq.join('<br/>'));
				return false;
			}
			top.$('##btnMCModalSave').attr('disabled', true);
			return true;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.classificationJS)#">

<cfoutput>
<div id="classification_err" class="alert alert-danger mb-2 d-none"></div>
<form name="frmClassification" id="frmClassification" method="post" class="p-2" action="#local.link.saveClassification#" onsubmit="return validateClassification();">
	<input type="hidden" name="siteResourceID" value="#local.siteResourceID#" />
	<input type="hidden" name="classificationID" value="#local.classificationID#" />
	<input type="hidden" name="selectorID" value="#local.selectorID#" />
	<input type="hidden" name="groupSetID" value="#local.gsid#" />

	<div class="form-group">
		<div class="form-label-group">
			<select name="allowSearch" id="allowSearch" class="custom-select">
				<option value="0"<cfif NOT val(local.allowSearch)> selected</cfif>>No</option>
				<option value="1"<cfif val(local.allowSearch)> selected</cfif>>Yes</option>
			</select>
			<label for="allowSearch">Display on Search Form *</label>
		</div>
	</div>
	<div class="form-group">
		<div class="form-label-group">
			<select name="showInSearchResults" id="showInSearchResults" class="custom-select">
				<option value="0"<cfif NOT val(local.showInSearchResults)> selected</cfif>>No</option>
				<option value="1"<cfif val(local.showInSearchResults)> selected</cfif>>Yes</option>
			</select>
			<label for="showInSearchResults">Display on Search Results *</label>
		</div>
	</div>
	<div class="form-group">
		<div class="form-label-group">
			<select id="groupSet" name="groupSet" onchange="$('##groupSetID').val($(this).val());" class="form-control" <cfif local.gsid NEQ 0>disabled</cfif>>
				<option value=""></option>
				<cfloop query="local.qryGroupSets">
					<option value="#local.qryGroupSets.groupSetID#"<cfif local.qryGetClassification.groupSetID eq local.qryGroupSets.groupSetID OR local.gsid eq local.qryGroupSets.groupSetID> SELECTED</cfif>>#left(local.qryGroupSets.groupSetName,70)#<cfif len(local.qryGroupSets.groupSetName) gt 70>...</cfif></option>
				</cfloop>
			</select>
			<label for="groupSet">Group Set for Classification *</label>
		</div>
	</div>
	<div class="form-group">
		<div class="form-label-group">
			<input type="text" id="nameOverride" name="nameOverride" value="#local.nameOverride#" class="form-control">
			<label for="nameOverride">Classification Name Override</label>
		</div>
		<div class="text-right font-size-md mt-1"><i>(optional)</i></div>
	</div>
	<div class="font-size-md mt-1">* required field</div>
	
	<button type="submit" name="btnSaveClassification" id="btnSaveClassification" class="d-none"></button>
</form>
</cfoutput>