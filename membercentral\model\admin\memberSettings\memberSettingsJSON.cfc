<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getclickedLinks" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"rt.detail")>
		<cfset arrayAppend(local.arrCols,"count(*)")>
		<cfset arrayAppend(local.arrCols,"count(distinct rt.recipientID)")>
		<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderBy')+1]# #arguments.event.getValue('orderDir')#">

		<cfquery name="local.qryClickedLinks" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpLinks') IS NOT NULL
				DROP TABLE ##tmpLinks;
			CREATE TABLE ##tmpLinks (url varchar(500), clicks int, uniqueClicks int, row int);

			DECLARE @siteID int, @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300);
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			INSERT INTO ##tmpLinks (url, clicks, uniqueClicks, row)
			select rt.detail as url, count(*) as clicks, count(distinct rt.recipientID) as uniqueClicks, 
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#) as row
			from dbo.email_messageRecipientHistory mrh
			inner join dbo.email_messageRecipientHistoryTracking rt
				on mrh.siteID = @siteID
				and mrh.messageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('messageID',0)#">
				and rt.siteID = @siteID
				and mrh.recipientID = rt.recipientID
			inner join dbo.email_statuses st on st.statusID = rt.statusID
				and st.statuscode = 'sg_click'
			<cfif len(local.searchValue)>
				WHERE rt.detail LIKE @searchValue
			</cfif>
			group by rt.detail
			having count(*) > 1;

			SET @totalCount = @@ROWCOUNT;

			select url, clicks, uniqueClicks, @totalCount AS totalCount, row
			from ##tmpLinks
			WHERE row > @posStart AND row <= @posStartAndCount
			ORDER BY row;
			
			IF OBJECT_ID('tempdb..##tmpLinks') IS NOT NULL
				DROP TABLE ##tmpLinks;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrClicks = []>
		<cfloop query="local.qryClickedLinks">
			<cfset local.arrClicks.append({
				"url": local.qryClickedLinks.url,
				"clicks": local.qryClickedLinks.clicks,
				"uniqueClicks": local.qryClickedLinks.uniqueClicks,
				"DT_RowId": "grpRow_#local.qryClickedLinks.row#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryClickedLinks.totalcount),
			"recordsFiltered": val(local.qryClickedLinks.totalcount),
			"data": local.arrClicks
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>
</cfcomponent>