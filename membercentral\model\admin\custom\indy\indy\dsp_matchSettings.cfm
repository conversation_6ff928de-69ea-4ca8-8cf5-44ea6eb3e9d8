<cfsavecontent variable="local.matchSettingsJS">
	<cfoutput>
	<script language="javascript">
		function editClassification(cID,srID){
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: (cID > 0 ? 'Edit' : 'Add') + ' Classification' ,
				iframe: true,
				contenturl: '#local.link.editClassification#&classificationID=' + cID + '&siteResourceID=' + srID,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSaveClassification").click',
					extrabuttonlabel: 'Save Details'
				}
			});
		}
		function removeClassification(cid) {
			var removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){ reloadClassifications(); }
				else {
					alert('We were unable to remove this classification. Try again.');
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
				}
			};

			let delBtn = $('##btnDel'+cid);
			mca_initConfirmButton(delBtn, function(){
				var objParams = { classificationID:cid };
				TS_AJX('ADMMEMBERSETTINGS','deleteClassification',objParams,removeResult,removeResult,10000,removeResult);
			});
		}
		/* functions for field set selector widget */
		function getAvailableAndSelectedFieldSetsForParticipant(onCompleteFunc){
			let objParams = { siteResourceID:#local.siteResourceID#, area:'matchSettings' };
			TS_AJX('FIELDSETWIDGET','getAvailableAndSelectedFieldSetsJSON',objParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}
		function createMemberFieldUsageForParticipant(fsID, onCompleteFunc) {
			let objParams = { fieldSetID:fsID, siteResourceID:#local.siteResourceID#, area:'matchSettings' };
			TS_AJX('FIELDSETWIDGET','createMemberFieldUsage',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
		function removeParticipantFieldUsage(useID, onCompleteFunc) {
			var objParams = { useID:useID };
			TS_AJX('FIELDSETWIDGET','fsRemove',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.matchSettingsJS)#">

<cfoutput>
<h4>Match Settings</h4>
<h5 class="mt-3">Participant Classifications</h5>
#local.participantGroupSetWidget.html#

<h5 class="mt-4">Participant Field Set</h5>
<div class="mb-2">Use these Member Field Sets for the "Match Tool" filter:</div>
#local.strParticipantFieldsSelector.html#
</cfoutput>