<cfsavecontent variable="local.classificationJS">
	<cfoutput>
	<script language="javascript">
		function startsWithAlpha(myString){
			var c = myString.charAt(0);
			if(isNaN(c)) return true;
			else return false;
		}
		function validateClassification() {
			var xName = $('##nameOverride').val();
			var sfs = $('##groupSetID').val();
			mca_hideAlert('classification_err');

			var arrReq = new Array();
			if (xName.length > 0) {
				if(!mca_isAlphaNumeric(xName)){ arrReq[arrReq.length] = 'The Classification Name Override can only contain letters and numbers.'; }
				if(!startsWithAlpha(xName)){ arrReq[arrReq.length] = 'The Classification Name Override must start with a letter.'; }
			}
			if (sfs == '') { arrReq[arrReq.length] = 'Choose a Group Set for this classification.'; }

			if (arrReq.length > 0) {
				mca_showAlert('classification_err', arrReq.join('<br/>'));
				return false;
			}
			top.$('##btnMCModalSave').attr('disabled', true);
			return true;
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.classificationJS)#">

<cfoutput>
<div id="classification_err" class="alert alert-danger mb-2 d-none"></div>
<form name="frmClassification" id="frmClassification" class="p-2" method="post" action="#local.formLink#" onsubmit="return validateClassification();">
	<input type="hidden" name="siteResourceID" value="#local.siteResourceID#">
	<input type="hidden" name="classificationID" value="#local.classificationID#">
	<input type="hidden" name="area" value="#arguments.event.getValue('area','')#">
	<input type="hidden" name="groupSetID" value="#local.gsid#" />

	<div class="form-group">
		<div class="form-label-group">
			<select name="showInSearchResults" id="showInSearchResults" class="form-control">
				<option value="0"<cfif NOT val(local.qryGetClassification.showInSearchResults)> selected</cfif>>No</option>
				<option value="1"<cfif val(local.qryGetClassification.showInSearchResults)> selected</cfif>>Yes</option>
			</select>
			<label for="showInSearchResults">Display to Solicitors in Front-End *</label>
		</div>
	</div>
	<div class="form-group">
		<div class="form-label-group">
			<select id="groupSet" name="groupSet" onchange="$('##groupSetID').val($(this).val());" class="form-control" <cfif local.gsid NEQ 0>disabled</cfif>>
				<option value=""></option>
				<cfloop query="local.qryGroupSets">
					<option value="#local.qryGroupSets.groupSetID#"<cfif local.qryGetClassification.groupSetID eq local.qryGroupSets.groupSetID OR local.gsid eq local.qryGroupSets.groupSetID> SELECTED</cfif>>#left(local.qryGroupSets.groupSetName,70)#<cfif len(local.qryGroupSets.groupSetName) gt 70>...</cfif></option>
				</cfloop>
			</select>
			<label for="groupSet">Group Set for Classification *</label>
		</div>
	</div>
	<div class="form-group">
		<div class="form-label-group">
			<input type="text" id="nameOverride" name="nameOverride" value="#local.qryGetClassification.name#" class="form-control">
			<div class="font-size-sm font-italic text-right"><i>(optional)</i></div>
			<label for="nameOverride" >Classification Name Override</label>
		</div>
	</div>
	<div class="form-group row no-gutters">
		<div class="col-sm-12">
			<div class="mt-2">* required field</div>
		</div>
	</div>
	<!--- hidden submit triggered from parent --->
	<button type="submit" name="btnSaveClassification" id="btnSaveClassification" class="d-none" ></button>
	
</form>
</cfoutput>