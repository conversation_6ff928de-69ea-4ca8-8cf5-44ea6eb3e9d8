function initTasksTable() {
	let arrCols = [
		{ "data": null,
			"render": function ( data, type, row, meta ) {
				return type === 'display' ? '<div>'+data.dateLastModified+'</div><div class="text-dim">'+data.taskStatus+'</div>' : data;
			},
			"className": "align-top",
			"width": "10%"
		},
		{ "data": null,
			"render": function ( data, type, row, meta ) {
				let renderData = '';
				if (type === 'display')	{
					if (data.deleteTaskQueueItemID) renderData += '<span class="float-right badge badge-neutral-danger text-danger"><i class="fa-solid fa-timer"></i> Deleting</span>';
					renderData += '<div><a href="javascript:editMember('+data.prospectMemberID+')">'+data.prospectMemberName+' ('+data.prospectMemberNumber+')</a></div>';
					renderData += '<div class="small text-dim">'+data.prospectMemberCompany+'</div>';
					renderData += '<div class="small text-dim">Created: '+data.dateEntered+'</div>';
					if(data.isDateDue)
						renderData += '<div class="small text-dim">Due: '+data.dateDue+'</div>';
				}
				return type === 'display' ? renderData : data;
			},
			"className": "align-top",
			"width": listTasksMode.toLowerCase() != 'projecttasks' ? "20%" : "40%"
		},
		{ "data": null,
			"render": function ( data, type, row, meta ) {
				let renderData = '';
				if (type === 'display')	{
					if(data.taskAssignees.length > 0) {
						data.taskAssignees.forEach((item,i) => {
							renderData += '<div'+(i>0 ? ' class="mt-2"' : '')+'><a href="javascript:editMember('+item.memberID+')">'+item.firstname+ ' ' + item.lastname +' ('+item.membernumber+')</a></div>';
							renderData += '<div class="small text-dim">'+item.company+'</div>';
						});
					}
				}
				return type === 'display' ? renderData : data;
			},
			"className": "align-top",
			"width": listTasksMode.toLowerCase() != 'projecttasks' ? "20%" : "40%",
			"orderable": false
		}
	];

	if (listTasksMode.toLowerCase() != 'projecttasks') {
		arrCols.push({
			"data": "projectName", "className": "align-top", "width": "40%"
		});
	}

	arrCols.push({
		"data": null,
		"render": function ( data, type, row, meta ) {
			let renderData = '';
			if (type === 'display')	{
				renderData += '<a href="#" class="btn btn-xs text-primary p-1 mr-1" onclick="viewItem('+ data.taskID+');return false;" title="View '+getTaskFieldLabel()+'"><i class="fa-solid fa-pen-to-square"></i></i></a>';
				renderData += '<a href="#" class="btn btn-xs text-danger p-1 mr-1'+(data.canDelete ? '' : ' invisible')+'" id="btnDelTsk'+data.taskID+'" '+(data.canDelete ? 'onclick="removeTask('+data.taskID+');return false;" title="Remove '+getTaskFieldLabel()+'"' : '')+'><i class="fa-solid fa-trash-alt"></i></a>';
				renderData += '<a href="#" class="btn btn-xs text-primary p-1'+(data.canRequestProfile ? '' : ' invisible')+'"'+(data.canRequestProfile ? ' onclick="assocCCToTask('+data.taskID+');return false;" title="Pay Method for '+getTaskFieldLabel()+'"' : '')+'>'+(data.taskPayProfileID ? '<i class="fa-solid fa-credit-card"></i>' : '<i class="fa-regular fa-credit-card-blank"></i>')+'</a>';
			}
			return type === 'display' ? renderData : data;
		},
		"className": "align-top text-center",
		"width": "10%",
		"orderable": false
	});
	
	taskListTable = $('#taskListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50, 100 ],
		"language": {
			"lengthMenu": "_MENU_"
		},
		"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
		"ajax": { 
			"url": link_listtasks + '&dtMode=' + listTasksMode,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilter').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": arrCols,
		"searching": false,
		"order": [[0,'desc']]
	});
}
function addTask() {
	if (Number(allowAddTask) == 0) return false;

	if (!$('#divTaskProject').is(':visible')) {
		$('div.taskdiv').hide();
		$('#divTaskProject').show();
	}
}
function doAddTask() {
	var selectedProjectID = 0;
	if (Number(allowAddTask) == 0) return false;
	else if (typeof project_id == 'undefined') {
		selectedProjectID = $('#projectID').val();
		if(selectedProjectID == null || selectedProjectID == '') {
			alert('Select a project to continue.');
			return false;
		} 
	}
	else selectedProjectID = project_id;

	if (ntLst && ntLst == 4) returnToAutomations();
	
	$('#divTaskGridContainer,div.taskdiv').hide();
	$('#divTaskFormArea').html('<h5>Add '+ getTaskFieldLabelPlural() +'</h5>' + mca_getLoadingHTML());
	$('#divTaskForm').show();
	$('#divTaskFormArea').load(link_addtask + '&ntLst=' + ntLst + '&pid=' + selectedProjectID);
}
function removeTask(id) {
	var removeItemResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			reloadTasksTable();
		} else {
			delTaskBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-alt"></i>');
			alert('We were unable to delete this task. Try again.');
		}
	};
	let delTaskBtn = $('#btnDelTsk'+id);
	mca_initConfirmButton(delTaskBtn, function(){
		let objParams = { taskID:id };
		TS_AJX('ADMINTASK','removeTask',objParams,removeItemResult,removeItemResult,10000,removeItemResult);
	});
}
function reloadClassificationTable(area){
	window[area + '_classificationsTable'].draw();
}
function viewItem(id) {
	if (ntLst && ntLst == 4) returnToAutomations();
	$('#divTaskGridContainer,div.taskdiv').hide();
	$('#divTaskFormArea').html('<h5>Edit Task</h5>' + mca_getLoadingHTML());
	$('#divTaskForm').show();
	$('#divTaskFormArea').load(link_viewtask + '&taskID=' + id + '&ntLst=' + ntLst);
}
function onCompleteSavingTasks() {
	$('#divTaskForm').hide(); 
	$('#divTaskGridContainer').show(); 
	filterTaskGrid(); 
	getTasksCountInTaskImportQueue();
}
function closeBox() { MCModalUtils.hideModal(); }

function editMember(id) {
	window.open(link_editmember + '&memberID=' + id);
}
function filterTask() {
	if (!$('#divFilterForm').is(':visible')) {
		$('div.taskdiv').hide();
		$('#divFilterForm').show();
	}
}
function autoFilter(myfield,e) {
	var keycode;
	if (window.event) keycode = window.event.keyCode;
	else if (e) keycode = e.which;
	else return true;
	if (keycode == 13) {
		filterTaskGrid();
		return false;
	} else {
		return true;
	}
}
function changeTaskFilterProspectType(atype) {
	if (atype != undefined) {
		if (atype == "group") selectProspectGroupTaskFilter();
		else selectProspectTaskFilter();
	}
}
function selectProspectTaskFilter() {	
	var selhref = link_selectmember + '&fldName=fProspectMemberID&retFunction=top.updateTaskFilterProspectField&dispTitle=' + escape('Filter '+ getTaskFieldLabelPlural() +' by Prospect');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getTaskFieldLabelPlural() +' by Prospect',
		iframe: true,
		contenturl: selhref,
		strmodalfooter : {
			classlist: 'd-none',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '',
			extrabuttonlabel: 'Submit',
		}
	});
}
function selectProspectGroupTaskFilter() {
	var selhref = link_selectgroup + '&fldName=fProspectGroupID&retFunction=top.updateTaskFilterProspectGroupField&dispTitle=' + escape('Filter '+ getTaskFieldLabelPlural() +' by Group');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getTaskFieldLabelPlural() +' by Group',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function updateTaskFilterProspectField(fldID,mID,mNum,mName) {
	var fld = $('#'+fldID);
	var fldName = $('#fProspectVal');
	fld.val(mID);
	if ((mName.length > 0) && (mNum.length > 0)) {
		fldName.html('<b>' + mName + ' (' + mNum + ')</b>&nbsp;');
		$('#fProspectGroupID').val("");
		$('#divProspectVal').show();
	} else {
		fldName.html('');
		$('#divProspectVal').hide();
	}
}
function updateTaskFilterProspectGroupField(fldID,gID,gPath) {
	var fld = $('#'+fldID);
	var fldName = $('#fProspectVal');	
	fld.val(gID);
	if (gPath.length > 0) {
		var newgPath = gPath.split("\\");
			newgPath.shift();
			newgPath = newgPath.join(" \\ ");
		fldName.html('<b>' + newgPath + '</b>&nbsp;');
		$('#fProspectMemberID').val("");
		$('#divProspectVal').show();
	} else {
		fldName.html('');
		$('#divProspectVal').hide();
	}
}
function clearTaskProspectType() {
	$(".fProspectType").each(function(){
		$(this).attr("checked",false);
	});
	$('#fProspectVal').html("");
	$('#fProspectMemberID').val("");
	$('#fProspectGroupID').val("");
	$('#divProspectVal').hide();
}
function changeTaskFilterSolicitorType(atype) {
	if (atype != undefined) {
		if (atype == "group") selectSolicitorGroupTaskFilter();
		else selectSolicitorTaskFilter();
	}
}
function selectSolicitorTaskFilter() {
	var selhref = link_selectmember + '&fldName=fSolicitorMemberID&retFunction=top.updateTaskFilterSolicitorField&dispTitle=' + escape('Filter '+ getTaskFieldLabelPlural() +' by Solicitor');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getTaskFieldLabelPlural() +' by Solicitor',
		iframe: true,
		contenturl: selhref,
		strmodalfooter : {
			classlist: 'd-none',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: '',
			extrabuttonlabel: 'Submit',
		}
	});
}
function selectSolicitorGroupTaskFilter() {
	var selhref = link_selectgroup + '&fldName=fSolicitorGroupID&retFunction=top.updateTaskFilterSolicitorGroupField&dispTitle=' + escape('Filter '+ getTaskFieldLabelPlural() +' by Group');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getTaskFieldLabelPlural() +' by Group',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function updateTaskFilterSolicitorField(fldID,mID,mNum,mName) {
	var fld = $('#'+fldID);
	var fldName = $('#fSolicitorVal');
	fld.val(mID);
	if ((mName.length > 0) && (mNum.length > 0)) {
		fldName.html('<b>' + mName + ' (' + mNum + ')</b>&nbsp;');
		$('#fSolicitorGroupID').val("");
		$('#divSolicitorVal').show();
	} else {
		fldName.html('');
		$('#divSolicitorVal').hide();
	}
}
function updateTaskFilterSolicitorGroupField(fldID,gID,gPath) {
	var fld = $('#'+fldID);
	var fldName = $('#fSolicitorVal');	
	fld.val(gID);
	if (gPath.length > 0) {
		var newgPath = gPath.split("\\");
			newgPath.shift();
			newgPath = newgPath.join(" \\ ");
		fldName.html('<b>' + newgPath + '</b>&nbsp;');
		$('#fSolicitorMemberID').val("");
		$('#divSolicitorVal').show();
	} else {
		fldName.html('');
		$('#divSolicitorVal').hide();
	}
}
function clearTaskSolicitorType() {
	$(".fSolicitorType").each(function(){
		$(this).attr("checked",false);
	});
	$('#fSolicitorVal').html("");
	$('#fSolicitorMemberID').val("");
	$('#fSolicitorGroupID').val("");
	$('#divSolicitorVal').hide();
}
function filterTaskGrid() {
	if (mctask_validateFields()) {
		reloadTasksTable();
		if (typeof project_id != 'undefined') { getTaskAutomations(); }
	}
}
function reloadTasksTable(){
	taskListTable.draw(false);
}
function clearfilterTaskGrid() {
	$('#frmFilter')[0].reset();
	$('#fWorkspaceTag').trigger('change');
	if (typeof project_id == 'undefined') { getWorkspaceProjectsAndTaskTags(); }
	if (typeof memberID == 'undefined') { clearTaskProspectType(); clearTaskSolicitorType(); }
	if ($('.divMCTaskFilterFields').length) { $('.divMCTaskFilterFields').html(''); }
	initTaskFilterFields();
	filterTaskGrid();
}
function massEmailTask() {
	if(taskListTable.page.info().recordsTotal == 0){
		alert('There are no filtered entries in tasks to act upon.');
		return false;
	}
	var emailLink = link_massemailtask + '&ntLst=' + ntLst + '&' + $('#frmFilter').serialize();
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'E-mail Filtered ' + getTaskFieldLabelPlural(),
		iframe: true,
		contenturl: emailLink,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function massChangeTask() {
	if (!$('#divMassChangeTasks').is(':visible')) {
		$('div.taskdiv').hide();
		$('#divMassChangeTasks').show();
	}
}
function massEditTask() {
	if(taskListTable.page.info().recordsTotal == 0){
		alert('There are no filtered entries in '+ getTaskFieldLabelPlural().toLowerCase() +' to act upon.');
		return false;
	}

	if (Number(allowMassEditTask) == 1) {
		if (ntLst == 4) returnToAutomations();
		var editLink = link_massedittask + '&ntLst=' + ntLst + '&' + $('#frmFilter').serialize();
		$('#divTaskGridContainer,div.taskdiv').hide();
		$('#divTaskFormArea').html('<h5>Mass Edit '+ getTaskFieldLabelPlural() +'</h5>' + mca_getLoadingHTML());
		$('#divTaskForm').show();
		$('#divTaskFormArea').load(editLink);
	}
}
function showMassChangeTaskUploadLoading() {
	mca_hideAlert('err_masschangetask');

	if ($('#frmMassChangeTasks .importFileControl').val() == ''){
		mca_showAlert('err_masschangetask','Select the CSV or XLS file from your computer.');
		return false;
	}

	$('#divMassChangeTaskForm').addClass('d-none');
	$('#frmMassChangeTasks :submit').prop('disabled',true);
	$('#divMassChangeTaskUploadLoading').html(mca_getLoadingHTML('Uploading and validating data. Please wait...')).removeClass('d-none');
	return true;
}
function exportTask() {
	if (mctask_validateFields()) {
		$('div.taskdiv').hide();
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Download Filtered Tasks',
			iframe: true,
			contenturl: link_exportPrompt + '&ntLst=' + ntLst + '&' + $('#frmFilter').serialize(),
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: 'exportTaskButtonHandler',
				extrabuttonlabel: 'Download CSV',
				extrabuttoniconclass: 'fa-light fa-file-csv'
			}
		});
	}
}
function exportTaskButtonHandler() {
	$('#MCModalBodyIframe')[0].contentWindow.fnDnTasks();
}
function doExportTask(fsid,pfldlist,tagfldlist) {	
	window.setTimeout(function(){ MCModalUtils.hideModal(); },2000);
	top.location.href = link_export + '&ntLst=' + ntLst + '&fsid=' + fsid + (pfldlist ? '&pfldlist=' + pfldlist : '') + (tagfldlist ? '&tagfldlist=' + tagfldlist : '') + '&' + $('#frmFilter').serialize();
	
}
function cancelTask(reloadTskGrid) {
	if (reloadTskGrid && typeof mcg_g != "undefined" && $('#ntLst').length && [1,2,3,4].indexOf(Number($('#ntLst').val())) != -1) {
		reloadTasksTable();
	}
	$("#divTaskFormArea").html('');
	$('#divTaskForm').hide();
	$('#divTaskGridContainer').show(300);
	return false;
}
function getTasksCountInTaskImportQueue() { 
	var tasksCountResults = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			if (Number(r.taskscount) > 0) {
				$('#divTasksInfoContainer')
					.html('<div class="alert d-flex align-items-center pl-2 align-content-center alert-info alert-dismissible fade show" role="alert">\
						<span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-info-circle"></i></span>\
						<span><strong class="d-block">'+getTaskFieldLabelPlural()+' Import Queue</strong> There are '+r.taskscount+' '+getTaskFieldLabelPlural()+' in the '+getTaskFieldLabelPlural()+' Import Queue which will be processed soon.</span>\
						<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button></div>')
					.removeClass('d-none');
			} else {
				$('#divTasksInfoContainer').html('').addClass('d-none');
			}
		} else {
			alert('We were unable to load '+getTaskFieldLabelPlural()+' import queue details.');
		}
	};

	if ([1,4].indexOf(Number(ntLst)) != -1) {
		var projectID = 0;
		if (typeof project_id != 'undefined') projectID = project_id;

		var objParams = { projectID:projectID };
		TS_AJX('ADMINTASK','getTasksCountInTaskImportQueue',objParams,tasksCountResults,tasksCountResults,20000,tasksCountResults);
	}
}
function doTaskFieldsValidate(taskfieldsWrapper) {
	var errorMsgArray = [];

	var thisInstance = $(taskfieldsWrapper);

	/*required fields*/
	var taskFieldRequired = thisInstance.find('input:text[data-mctaskfieldisrequired="1"], select[data-mctaskfieldisrequired="1"], textarea[data-mctaskfieldisrequired="1"]').not(':hidden').not(':disabled');

	/*distinct radio, checkbox elements*/
	var radioCheckBoxElements = thisInstance.find('input:radio[data-mctaskfieldisrequired="1"], input:checkbox[data-mctaskfieldisrequired="1"]');

	var elemArr = [];
	$.each( radioCheckBoxElements, function() {
		var elemName = this.name;
		if( $.inArray( elemName, elemArr ) < 0 ){
			elemArr.push(elemName);
			taskFieldRequired.push(this);
		}
	});

	var taskFieldRequiredErrorMsgArray = $.map(taskFieldRequired,validateMCTask_fieldIsRequired);
	Array.prototype.push.apply(errorMsgArray, taskFieldRequiredErrorMsgArray);

	/*text controls offering whole number*/
	var textControlIntegerCustomField = thisInstance.find('input[data-mctaskfielddisplaytypecode="TEXTBOX"][data-mctaskfielddatatypecode="INTEGER"]').not(':hidden').not(':disabled');
	var textControlIntegerCustomFieldErrorMsgArray = $.map(textControlIntegerCustomField,validateMCTask_textControlValidInteger);
	Array.prototype.push.apply(errorMsgArray, textControlIntegerCustomFieldErrorMsgArray);

	/*text controls offering decimal number*/
	var textControlDecimalCustomField = thisInstance.find('input[data-mctaskfielddisplaytypecode="TEXTBOX"][data-mctaskfielddatatypecode="DECIMAL2"]').not(':hidden').not(':disabled');
	var textControlDecimalCustomFieldErrorMsgArray = $.map(textControlDecimalCustomField,validateMCTask_textControlValidDecimal);
	Array.prototype.push.apply(errorMsgArray, textControlDecimalCustomFieldErrorMsgArray);

	/*drop empty elements*/
	var finalErrors = $.map(errorMsgArray, function(thisError){
		if (thisError.length) return thisError;
		else return null;
	});
	
	return finalErrors;
}
function validateMCTask_fieldIsRequired(thisField) {
	var fld = $(thisField);
	var fldName = $(thisField).attr('name');
	var displayTypeCode = fld.data('mctaskfielddisplaytypecode');
	var returnMsg = '';

	switch(displayTypeCode) {
		case 'TEXTBOX':
		case 'TEXTAREA':
		case 'DATE':
			if (fld.val() == '') {
				returnMsg =  fld.data('mctaskfieldrequiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
			}
		break;
		case 'SELECT':
			if (fld.val() == '' && fld.find('option:not(:disabled)').length > 1) {
				returnMsg =  fld.data('mctaskfieldrequiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
			}
		break;
		case 'RADIO':
		case 'CHECKBOX':
			if ($('input[name="' + fldName + '"]').not(':disabled').length > 0 && !$('input[name="' + fldName + '"]').not(':disabled').is(':checked')) {
				returnMsg =  fld.data('mctaskfieldrequiredmsg').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;');
			}
		break;
	}
	return returnMsg;
}
function validateMCTask_textControlValidInteger(thisField) {
	var returnMsg = '';
	var fld = $(thisField);
	var fldval = Number(fld.val().trim());

	if (fldval != '' && fldval !== parseInt(fldval)) {
		returnMsg = 'Enter a valid whole number for ' + fld.data('mctaskfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
	}
	return returnMsg;
}
function validateMCTask_textControlValidDecimal(thisField) {
	var returnMsg = '';
	var fld = $(thisField);
	var fldval = Number(fld.val().trim());

	if (fldval != '') {
		if (fldval !== parseFloat(fldval)) {
			returnMsg = 'Enter a valid decimal number for ' + fld.data('mctaskfielddesc').replace(/&/g,'&amp;').replace(/</g,'&lt;').replace(/>/g,'&gt;') + '.';
		}
	}
	return returnMsg;
}
function initializeTaskFieldsControls (scope, mode) {
	if(mode === undefined) {
		mode = 'init';
	}
	mca_setupMultipleDatePickerFields(scope,'MCAdminDateControl');

	scope.find('.MCAdminDateControlClearLink').click(function(event){
		var linkedDateControlID = $(this).data('linkeddatecontrol');
		$('#' + linkedDateControlID).val('').change();
		event.preventDefault();
	});
}
hideAlert = function() { $('#task_frm_err').html('').hide(); };
showAlert = function(msg) { $('#task_frm_err').html(msg).show(); };

function getWorkspaceProjectsAndTaskTags() {
	var workspaceid = Number($('select#fWorkspaceID').val());

	$('#fProjectID').find('option').not(':first').remove();

	var objParams = { workspaceid:workspaceid };
	$.getJSON('/?event=proxy.ts_json&c=ADMINTASK&m=getWorkspaceProjects', objParams)
		.done(populateWorkspaceProjects)
		.fail(populateWorkspaceProjects); 

	if (workspaceid > 0) {
		loadTaskTags(workspaceid);
	} else {
		$('#fWorkspaceTag').find('option').remove();
		$('#fWorkspaceTag').trigger('change');
		$('#fWorkspaceTagHolder').addClass('d-none');
	}
}
function populateWorkspaceProjects(r) { 
	if(r.arrprojects.length) {
		$.each(r.arrprojects, function (i, item) {
			$('#fProjectID').append( $('<option>', { value: item.projectid, text : item.projectname }) );
		}); 
	}
	return false;
}
function loadTaskTags(wid) {
	var objParams = { workspaceid:wid };
	$.getJSON('/?event=proxy.ts_json&c=ADMINPROJECT&m=getWorkspaceTaskTags', objParams)
		.done(populateTaskTagsForFilters)
		.fail(populateTaskTagsForFilters);
}
function populateTaskTagsForFilters(r) { 
	var hasWorkspaceFilter = 1;
	if (typeof isProjectProspectFilters != "undefined") 
		hasWorkspaceFilter = 0;
	var noneSelectedText = 'Any '+ getTaskFieldLabel() +' Tags';
	if(r.arrtasktags.length) {
		var catTree = '';
		var newOpts = '';
		$.each(r.arrtasktags, function (i, item) {
			if (catTree != item.categorytree) {
				catTree = item.categorytree;
				if (i > 0) {
					newOpts += '</optgroup>';
				}
				newOpts += '<optgroup label="'+catTree+'"><option value="'+item.categoryid+'">'+item.category+'</option>';
			} else {
				newOpts += '<option value="'+item.categoryid+'">'+item.category+'</option>';
			}

			if (i + 1 == r.arrtasktags.length) {
				newOpts += '</optgroup>';
			}
		});		
		$('#fWorkspaceTag').find('optgroup').remove().end().append(newOpts);
		$('#fWorkspaceTag').trigger('change');

		if(hasWorkspaceFilter){
			$('#fWorkspaceTagHolder').removeClass('d-none');
		}		
	} else {
		if(hasWorkspaceFilter){
			$('#fWorkspaceTagHolder').addClass('d-none');
		}
	}
	return false;
}
function loadTaskChangeHistory(at,tid) {
	$('div#div_task_chghist').html(mca_getLoadingHTML('Loading history...')).load(
		link_taskChangeHistory+'&at='+at+'&taskID='+tid,
		function(response, status, xhr) { 
			if (status == 'error') {
				$(this).html('Unable to load change history for this task.');
			}
		});
}
function toggleATRow(rowID) {
	var st = $('#atChanges_' + rowID).css("display");
	if (st == 'none') {
		$('#atChanges_' + rowID).show();
		$('#atTreeImg_' + rowID).html('<i class="fa-regular fa-minus-square"></i>');
	} else {
		$('#atChanges_' + rowID).hide();
		$('#atTreeImg_' + rowID).html('<i class="fa-regular fa-plus-square"></i>');
	}
}
function initTaskFilterFields() {
	var projectID = 0;
	var workspaceID = 0;
	var categoryIDList = $('#fWorkspaceTag').val() || '';
	if (categoryIDList != '') categoryIDList = categoryIDList.join(',');

	if (typeof project_id == 'undefined') projectID = $('#fProjectID').val();
	else projectID = project_id;

	if (typeof workspace_id == 'undefined') workspaceID = $('#fWorkspaceID').val();
	else workspaceID = workspace_id;

	var objParams = { workspaceid:workspaceID, projectid:projectID, categoryIDList:categoryIDList, operationMode:'taskfilter' };
	$.getJSON('/?event=proxy.ts_json&c=ADMINPROJECT&m=getTaskFieldFilterSelector', objParams)
		.done(populateTaskFilterFields)
		.fail(populateTaskFilterFields);
}
function populateTaskFilterFields(r) {
	$('#divAdvFilters').hide();
	$('#divTFieldArea').html('');
	
	if (r.hasFields == true) {
		$('#divTaskFilterFieldsHolder').html(r.fieldSelectControls);
		$('#divAdvFilters').show();
	}
}
function mctask_showFieldValueArea() {
	var divcount = $('#divTFieldArea div').length;
	var obj = { newItem:divcount++ };

	var taskFieldFilterTemplateSource = $('#mc_taskFieldFilterTempate').html();
	var taskFieldFilterTemplate = Handlebars.compile(taskFieldFilterTemplateSource);
	$('#divTFieldArea').append(taskFieldFilterTemplate(obj));
	$('#mctaskFieldID'+obj.newItem).append($('#mcTaskFilterFields').html());
}
function mctask_copyFieldValueArea(x) {
	var sel = $('#mctaskFieldID'+x);
	var divSelected = $('#mctaskFieldIDSelected'+x);
	var divControl = $('#mctaskFieldData'+x);
	var divErr = $('#mctaskFieldDataErr'+x);
	if (sel.val() == '') {
		divSelected.addClass('d-none').html('');
		divControl.html('');
		divErr.html('');
	} else {
		var selSelected = $('#mctaskFieldID'+x+' :selected');
		var selOptionLabel = selSelected.text();
		var selGroupLabel = selSelected.parent().attr('label');
		divSelected.removeClass('d-none').html(selGroupLabel + ': ' + selOptionLabel);

		sel.parent().addClass('d-none');

		var rfarea = $('#divTF_'+sel.val());
		var rfareaclonehtml = rfarea.clone().html();
		divControl.html(rfareaclonehtml.replace(/{{x}}/gi,x));
		divControl.find('div[data-displayTypeCode="DATE"]').each(function(i) { 
			mca_setupDatePickerRangeFields($(this).attr('id')+'_lower',$(this).attr('id')+'_upper');
		});
	}
}
function mctask_hideFieldValueArea(x) {
	$('#divMCTaskFilterField'+x).html('').hide();
}
function mctask_validateFieldText(el) {
	var dt = el.attr('data-dataTypeCode');
	var val = el.val();
	if (dt == 'STRING' && val.length == 0) {
		el.closest('.mctaskFieldData').next().html('<i class="fa-solid fa-exclamation-triangle fa-lg"></i>');
	} else if (dt == 'DECIMAL2' && !mctask_validateFieldText_Decimal(val)) {
		el.closest('.mctaskFieldData').next().html('<i class="fa-solid fa-exclamation-triangle fa-lg"></i>');
	} else if (dt == 'INTEGER' && !mctask_validateFieldText_Integer(val)) {
		el.closest('.mctaskFieldData').next().html('<i class="fa-solid fa-exclamation-triangle fa-lg"></i>');
	} else {
		el.closest('.mctaskFieldData').next().html('');
	}
}
function mctask_validateFieldText_Integer(val) {
	var fldval = Number($.trim(val));
	if (fldval != '') {
		if (fldval !== parseInt(fldval)) return false;
		else return true;
	} else {
		return false;
	}
}
function mctask_validateFieldText_Decimal(val) {
	var fldval = Number($.trim(val));
	if (fldval != '') {
		if (fldval !== parseFloat(fldval)) return false;
		else return true;
	} else {
		return false;
	}
}
function mctask_validateFieldSelect(el) {
	if (el.val() == '') {
		el.closest('.mctaskFieldData').next().html('<i class="fa-solid fa-exclamation-triangle fa-lg"></i>');
	} else {
		el.closest('.mctaskFieldData').next().html('');	
	}
}
function mctask_validateFieldDate(elLower,elUpper) {
	if ($('#'+elLower).val() == '' || $('#'+elUpper).val() == '') {
		$('#'+elLower).closest('.mctaskFieldData').next().html('<i class="fa-solid fa-exclamation-triangle fa-lg"></i>');
	} else {
		$('#'+elLower).closest('.mctaskFieldData').next().html('');	
	}
}
function mctask_validateFields() {
	$('#divTFieldArea div select.mctaskFieldSelector').not(':disabled').each(function(i) { 
		var sel = $(this);
		if (sel.val() != '') {
			sel.parent().next().find('input[data-displayTypeCode="TEXTBOX"], input[data-displayTypeCode="TEXTAREA"]').each(function(j) { 
				var exp = $(this).closest('.mctaskFieldData').find('select.mctaskfieldexpression').val();
				if (!(exp == 'exists' || exp == 'not_exists')) mctask_validateFieldText($(this));
			});
			sel.parent().next().find('select[data-displayTypeCode="SELECT"], select[data-displayTypeCode="CHECKBOX"], select[data-displayTypeCode="RADIO"]').each(function(j) { 
				var exp = $(this).closest('.mctaskFieldData').find('select.mctaskfieldexpression').val();
				if (!(exp == 'exists' || exp == 'not_exists')) mctask_validateFieldSelect($(this));
			});
			sel.parent().next().find('div[data-displayTypeCode="DATE"]').each(function(k) { 
				var exp = $(this).closest('.mctaskFieldData').find('select.mctaskfieldexpression').val();
				if (!(exp == 'exists' || exp == 'not_exists')) mctask_validateFieldDate($(this).attr('id')+'_lower',$(this).attr('id')+'_upper');
			});
		}
	});
	if ($('#divTFieldArea i.fa-exclamation-triangle').length == 0) return true;
	else return false;
}
function mctask_changeExpression(el) {
	var sel = el.val();
	if (sel == 'exists' || sel == 'not_exists') {
		el.parent().next().find('input,select').val('');
		el.parent().next().hide();
	} else {
		el.parent().next().show();
	} 
	el.closest('.mctaskFieldData').next().html('');
}
function getTaskFieldLabel(){
	if (typeof mc_taskfieldlabel != 'undefined') return mc_taskfieldlabel;
	return "Task";	
}
function getTaskFieldLabelPlural(){
	if (typeof mc_taskfieldlabelplural != 'undefined') return mc_taskfieldlabelplural;
	return "Tasks";	
}
function getSolicitorFieldLabel(){
	if (typeof mc_solicitorfieldlabel != 'undefined') return mc_solicitorfieldlabel;
	return "Solicitor";	
}
function getSolicitorFieldLabelPlural(){
	if (typeof mc_solicitorfieldlabelplural != 'undefined') return mc_solicitorfieldlabelplural;
	return "Solicitors";	
}
function gotoMemberTasksTab(id) {
	window.open(link_editmember + '&memberID=' + id + '&tab=tasks');
}
function initSolicitorsTable(){
	solicitorListTable = $('#solicitorListTable').DataTable({
		"processing": true,
		"serverSide": true,
		"pageLength": 10,
		"lengthMenu": [ 10, 25, 50, 100 ],
		"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
		"ajax": { 
			"url": link_listsolicitors,
			"type": "post",
			"data": function(d) {
				$.each($('#frmFilterSolicitor').serializeArray(),function() {
					d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
				});
			}
		},
		"autoWidth": false,
		"columns": [
			{ "data": null,
				"render": function ( data, type, row, meta ) {
					let renderData = '';
					if (type === 'display')	{
						renderData += '<div><a href="javascript:editMember('+data.solicitormemberid+')">'+data.solicitormembername+'</a></div>';
						if (data.solicitormembercompany.length)
							renderData += '<div class="small text-dim">'+data.solicitormembercompany+'</div>';
					}
					return type === 'display' ? renderData : data;
				},
				"className": "align-top",
				"width": "80%"
			},
			{ "data": "taskscount", "width": "20%", "className": "align-top" },
		],
		"order": [[0, 'asc']],
		"searching": false
	});
}
function filterSolicitors() {
	if ($('#divFilterSolicitorForm').hasClass('d-none')) {
		$('div.solicitordiv').addClass('d-none');
		$('#divFilterSolicitorForm').removeClass('d-none');
	}
}
function filterSolicitorsGrid() {
	solicitorListTable.draw(false);
}
function clearfilterSolicitorsGrid() {
	$('#frmFilterSolicitor')[0].reset();
	clearProjectSolicitorType();
	filterSolicitorsGrid();
}
function changeProjectFilterSolicitorType(atype) {
	if (atype != undefined) {
		if (atype == "group") selectProjSolicitorGroupTaskFilter();
		else selectProjSolicitorTaskFilter();
	}
}
function selectProjSolicitorTaskFilter() {
	var selhref = link_selectmember + '&fldName=fProjSolicitorMemberID&retFunction=top.updateProjFilterSolicitorField&dispTitle=' + escape('Filter '+ getSolicitorFieldLabelPlural() +' by Member');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getSolicitorFieldLabelPlural() +' by Member',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function selectProjSolicitorGroupTaskFilter() {
	var selhref = link_selectgroup + '&fldName=fProjSolicitorGroupID&retFunction=top.updateProjFilterSolicitorGroupField&dispTitle=' + escape('Filter '+ getSolicitorFieldLabelPlural() +' by Group');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getSolicitorFieldLabelPlural() +' by Group',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function updateProjFilterSolicitorField(fldID,mID,mNum,mName) {
	var fld = $('#'+fldID);
	var fldName = $('#fProjSolicitorVal');
	fld.val(mID);
	if ((mName.length > 0) && (mNum.length > 0)) {
		fldName.html('<b>' + mName + ' (' + mNum + ')</b>&nbsp;');
		$('#fProjSolicitorGroupID').val("");
		$('#divProjSolicitorVal').removeClass('d-none');
	} else {
		fldName.html('');
		$('#divProjSolicitorVal').addClass('d-none');
	}
}
function updateProjFilterSolicitorGroupField(fldID,gID,gPath) {
	var fld = $('#'+fldID);
	var fldName = $('#fProjSolicitorVal');	
	fld.val(gID);
	if (gPath.length > 0) {
		var newgPath = gPath.split("\\");
			newgPath.shift();
			newgPath = newgPath.join(" \\ ");
		fldName.html('<b>' + newgPath + '</b>&nbsp;');
		$('#fProjSolicitorMemberID').val("");
		$('#divProjSolicitorVal').removeClass('d-none');
	} else {
		fldName.html('');
		$('#divProjSolicitorVal').addClass('d-none');
	}
}
function clearProjectSolicitorType() {
	$(".fProjSolicitorType").each(function(){
		$(this).attr("checked",false);
	});
	$('#fProjSolicitorVal').html("");
	$('#fProjSolicitorMemberID').val("");
	$('#fProjSolicitorGroupID').val("");
	$('#divProjSolicitorVal').addClass('d-none');
}
function exportSolicitors() {
	if(solicitorListTable.page.info().recordsTotal == 0){
		alert('There are no filtered entries in ' + getSolicitorFieldLabelPlural() + ' to act upon.');
		return false;
	}
	
	$('div.solicitordiv').addClass('d-none');

	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Export ' + getSolicitorFieldLabelPlural(),
		iframe: true,
		contenturl: link_exportsolicitorsprompt,
		strmodalfooter: {
			classlist: 'text-right',
			showclose: false,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary',
			extrabuttononclickhandler: 'exportSolicitorsButtonHandler',
			extrabuttonlabel: 'Export',
			extrabuttoniconclass: 'fa-light fa-file-csv'
		}
	});
}
function exportSolicitorsButtonHandler(){
	$('#MCModalBodyIframe')[0].contentWindow.doExportSolicitors();
}
function getFilterDataForExportSolicitors() {
	return $('#frmFilterSolicitor').serialize();
}
function massEmailSolicitors() {
	if(solicitorListTable.page.info().recordsTotal == 0){
		alert('There are no filtered entries in ' + getSolicitorFieldLabelPlural() + ' to act upon.');
		return false;
	}
	var emailLink = link_massemailsolicitors + '&' + $('#frmFilterSolicitor').serialize();
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'E-mail Filtered ' + getSolicitorFieldLabelPlural(),
		iframe: true,
		contenturl: emailLink,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function assocCCToTask(tid) {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Associate Pay Method for',
		iframe: true,
		contenturl: link_assocCCToTask + '&tid=' + tid,
		strmodalfooter: {
			classlist: 'text-right',
			showclose: true,
			showextrabutton: false
		}
	});
}
function editTaskAutomation(aid) {
	if (Number(allowMassEditTask) == 1) {
		if (typeof mcg_g != "undefined") {
			cancelTask();
			clearfilterTaskGrid();
		}
		var editLink = link_edittasksautomation + '&ntLst=' + ntLst + '&aid=' + aid;
		$('#divTasksAutomationContainer').addClass('d-none');
		$('#divTaskAutomationFormArea').html('<h5>Edit Automation</h5>' + mca_getLoadingHTML());
		$('#divTaskAutomationForm').removeClass('d-none');
		$('#divTaskAutomationFormArea').load(editLink);
	}
}

/*automations*/
function populateTaskTagsForFiltersForTA(r) { 
	var noneSelectedText = 'Any '+ getTaskFieldLabel() +' Tags';
	if(r.arrtasktags.length) {
		var catTree = '';
		var newOpts = '';
		$.each(r.arrtasktags, function (i, item) {
			if (catTree != item.categorytree) {
				catTree = item.categorytree;
				if (i > 0) {
					newOpts += '</optgroup>';
				}
				newOpts += '<optgroup label="'+catTree+'"><option value="'+item.categoryid+'">'+item.category+'</option>';
			} else {
				newOpts += '<option value="'+item.categoryid+'">'+item.category+'</option>';
			}

			if (i + 1 == r.arrtasktags.length) {
				newOpts += '</optgroup>';
			}
		});		
		$('#fWorkspaceTagForTA').find('optgroup').remove().end().append(newOpts);
		if (strTaskAutomation.strTaskFilters.fWorkspaceTag != '') $('#fWorkspaceTagForTA').val(strTaskAutomation.strTaskFilters.fWorkspaceTag.split(','));
	}
	return false;
}

function initTaskFilterFieldsForTA() {
	var objParams = { workspaceid:workspace_id, projectid:project_id, categoryIDList:'', operationMode:'editautomation' };
	$.getJSON('/?event=proxy.ts_json&c=ADMINPROJECT&m=getTaskFieldFilterSelector', objParams)
		.done(populateTaskFilterFieldsForTA)
		.fail(populateTaskFilterFieldsForTA);
}
function populateTaskFilterFieldsForTA(r) {
	$('#divAdvFiltersForTA').hide();
	$('#divTFieldAreaForTA').html('');
	
	if (r.hasFields == true) {
		$('#divTaskFilterFieldsHolderForTA').html(r.fieldSelectControls);
		$('#divAdvFiltersForTA').show();
	}
	initAutomationFormFilters();
}
function initAutomationFormFilters() {
	var filterFormContainer = $('#divAutomationTasksFilterForm');
	for(var filterfield in strTaskAutomation.strTaskFilters) { 
		if (filterfield.substring(0,13) == 'mctaskFieldID') {
			mctask_showFieldValueAreaForTA();
			var fieldSel = strTaskAutomation.strTaskFilters[filterfield];
			var expressionSelectID = 'TF_'+fieldSel.split('_')[0]+'_exp';
			var expressionVal = strTaskAutomation.strTaskFilters[expressionSelectID];
			
			filterFormContainer.find('select.mctaskFieldSelector:last').val(fieldSel).trigger('change');
			filterFormContainer.find('select.mctaskfieldexpression:last').val(expressionVal).trigger('change');
			if (expressionVal == 'eq' || expressionVal == 'contains') {
				var fieldValID = 'TF_'+fieldSel.split('_')[0];
				filterFormContainer.find('[name="'+fieldValID+'"]').val(strTaskAutomation.strTaskFilters[fieldValID]);
			} else if (expressionVal == 'between') {
				var fieldValLowerID = 'TF_'+fieldSel.split('_')[0]+'_lower';
				var fieldValUpperID = 'TF_'+fieldSel.split('_')[0]+'_upper';
				filterFormContainer.find('input[name="'+fieldValLowerID+'"]').val(strTaskAutomation.strTaskFilters[fieldValLowerID]);
				filterFormContainer.find('input[name="'+fieldValUpperID+'"]').val(strTaskAutomation.strTaskFilters[fieldValUpperID]);
			}
		}
	}
}
function getTaskMemberDetails(mnum,fldID) { 
	var memberResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true'){
			updateTaskField(fldID, r.memberid, r.membernumber, r.firstname + ' ' + r.lastname);
		} else {
			alert('We were unable to load member details.');
		}
	};
	
	var objParams = { memberNumber:mnum };
	TS_AJX('ADMINTASK','getTaskMemberDetailsFromMemberNumber',objParams,memberResult,memberResult,10000,memberResult);
}
function mctask_showFieldValueAreaForTA() {
	var divcount = $('#divTFieldAreaForTA div').length;
	var obj = { newItem:divcount++ };

	var taskFieldFilterTemplateSource = $('#mc_taskFieldFilterTempate').html();
	var taskFieldFilterTemplate = Handlebars.compile(taskFieldFilterTemplateSource);
	$('#divTFieldAreaForTA').append(taskFieldFilterTemplate(obj));
	$('#mctaskFieldID'+obj.newItem).append($('#mcTaskFilterFields').html());
}
function changeTaskFilterProspectTypeForTA(atype) {
	if (atype != undefined) {
		if (atype == "group") selectProspectGroupTaskFilterForTA();
		else selectProspectTaskFilterForTA();
	}
}
function selectProspectTaskFilterForTA() {	
	var selhref = link_selectmember + '&fldName=fProspectMemberIDForTA&retFunction=top.updateTaskFilterProspectFieldForTA&dispTitle=' + escape('Filter '+ getTaskFieldLabelPlural() +' by Prospect');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getTaskFieldLabelPlural() +' by Prospect',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function selectProspectGroupTaskFilterForTA() {
	var selhref = link_selectgroup + '&fldName=fProspectGroupIDForTA&retFunction=top.updateTaskFilterProspectGroupFieldForTA&dispTitle=' + escape('Filter '+ getTaskFieldLabelPlural() +' by Group');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getTaskFieldLabelPlural() +' by Group',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function updateTaskFilterProspectFieldForTA(fldID,mID,mNum,mName) {
	var fld = $('#'+fldID);
	var fldName = $('#fProspectValForTA');
	fld.val(mID);
	if ((mName.length > 0) && (mNum.length > 0)) {
		fldName.html('<b>' + mName + ' (' + mNum + ')</b>&nbsp;');
		$('#fProspectGroupIDForTA').val("");
		$('#divProspectValForTA').show();
	} else {
		fldName.html('');
		$('#divProspectValForTA').hide();
	}
}
function updateTaskFilterProspectGroupFieldForTA(fldID,gID,gPath) {
	var fld = $('#'+fldID);
	var fldName = $('#fProspectValForTA');	
	fld.val(gID);
	if (gPath.length > 0) {
		var newgPath = gPath.split("\\");
			newgPath.shift();
			newgPath = newgPath.join(" \\ ");
		fldName.html('<b>' + newgPath + '</b>&nbsp;');
		$('#fProspectMemberIDForTA').val("");
		$('#divProspectValForTA').show();
	} else {
		fldName.html('');
		$('#divProspectValForTA').hide();
	}
}
function clearTaskProspectTypeForTA() {
	$(".fProspectTypeForTA").each(function(){
		$(this).attr("checked",false);
	});
	$('#fProspectValForTA').html("");
	$('#fProspectMemberIDForTA').val("");
	$('#fProspectGroupIDForTA').val("");
	$('#divProspectValForTA').hide();
}
function changeTaskFilterSolicitorTypeForTA(atype) {
	if (atype != undefined) {
		if (atype == "group") selectSolicitorGroupTaskFilterForTA();
		else selectSolicitorTaskFilterForTA();
	}
}
function selectSolicitorTaskFilterForTA() {
	var selhref = link_selectmember + '&fldName=fSolicitorMemberIDForTA&retFunction=top.updateTaskFilterSolicitorFieldForTA&dispTitle=';
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getTaskFieldLabelPlural() +' by Solicitor',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function selectSolicitorGroupTaskFilterForTA() {
	var selhref = link_selectgroup + '&fldName=fSolicitorGroupIDForTA&retFunction=top.updateTaskFilterSolicitorGroupFieldForTA&dispTitle=' + escape('Filter '+ getTaskFieldLabelPlural() +' by Group');
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Filter '+ getTaskFieldLabelPlural() +' by Group',
		iframe: true,
		contenturl: selhref,
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}
function updateTaskFilterSolicitorFieldForTA(fldID,mID,mNum,mName) {
	var fld = $('#'+fldID);
	var fldName = $('#fSolicitorValForTA');
	fld.val(mID);
	if ((mName.length > 0) && (mNum.length > 0)) {
		fldName.html('<b>' + mName + ' (' + mNum + ')</b>&nbsp;');
		$('#fSolicitorGroupIDForTA').val("");
		$('#divSolicitorValForTA').show();
	} else {
		fldName.html('');
		$('#divSolicitorValForTA').hide();
	}
}
function updateTaskFilterSolicitorGroupFieldForTA(fldID,gID,gPath) {
	var fld = $('#'+fldID);
	var fldName = $('#fSolicitorValForTA');	
	fld.val(gID);
	if (gPath.length > 0) {
		var newgPath = gPath.split("\\");
			newgPath.shift();
			newgPath = newgPath.join(" \\ ");
		fldName.html('<b>' + newgPath + '</b>&nbsp;');
		$('#fSolicitorMemberIDForTA').val("");
		$('#divSolicitorValForTA').show();
	} else {
		fldName.html('');
		$('#divSolicitorValForTA').hide();
	}
}
function clearTaskSolicitorTypeForTA() {
	$(".fSolicitorTypeForTA").each(function(){
		$(this).attr("checked",false);
	});
	$('#fSolicitorValForTA').html("");
	$('#fSolicitorMemberIDForTA').val("");
	$('#fSolicitorGroupIDForTA').val("");
	$('#divSolicitorValForTA').hide();
}
function filterTasksGridForTA() {
	if (mctask_validateFields()) {
		reloadAutomationTasks();
	}
}

function clearfilterTaskGridForTA() {
	strTaskAutomation.strTaskFilters = { fProjectID:project_id };
	$('#divAutomationTasksFilterForm').find(':input').each(function() {
		if(this.type == 'checkbox' || this.type == 'radio') {
			this.checked = false;
		} else if (this.type != 'hidden') { 
			$(this).val(''); 

			if ($(this).hasClass('rolldate')) {
				var mcrdfld = $(this).attr('name');
				updateTasksAFExample($(this).attr('id'),'roll_' + mcrdfld + '_afid','divroll_' + mcrdfld + '_afid');
			}
		}
	});
	$('#fWorkspaceTagForTA').trigger('change');
	clearTaskProspectTypeForTA(); 
	clearTaskSolicitorTypeForTA();
	$('#divAdvFiltersForTA').find('.divMCTaskFilterFields').html('');
	filterTasksGridForTA();
}
function returnToAutomations() {
	$("#divTaskAutomationFormArea").html('');
	$('#divTaskAutomationForm').addClass('d-none');
	$('#divTasksAutomationContainer').removeClass('d-none');
	return false;
}

/* Rolling Dates */
function writeTasksRollDates() {
	var handleResponse = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') {
			var tempsel = $('<select>');
			tempsel.append('<option value="0">No Advance</option>');
			$.each(r.qryaf, function(i) {
				tempsel.append('<option value="' + r.qryaf[i].afid + '">' + r.qryaf[i].afname + '</option>');
			});

			var sectionTemplate = '<div class="row mt-3"><div class="col-sm-12">{{content}}</div></div>';
			var sectionRowTemplate = '\
				<div id="{{divID}}" class="form-group row {{divClassList}}">\
					<label for="{{labelfor}}" class="col-sm-3 col-form-label-sm font-size-md pl-5">{{label}}</label>\
					<div class="col-sm-8"><div class="input-group input-group-sm">{{content}}</div></div>\
				</div>';
			var rowControls = '';
			var onchangeFunctions = '';
			var resultRows = '';

			$('input.rolldate').each(function() {
				var controlID = 'roll_' + $(this).attr('name') + '_afid';
				var controlDivID = 'div_' + controlID;
				var controlDivClassList = 'mc_taskfield_afid d-none';

				onchangeFunctions = "updateTasksAFExample('"+ $(this).attr('id') +"','"+ controlID +"','div"+ controlID +"')";
				rowControls = '<select class="rollafid form-control form-control-sm" name="'+ controlID +'" id="'+ controlID +'" onchange="'+ onchangeFunctions +'"></select> <div id="div'+ controlID +'" class="ml-2" style="min-width:100px;">&lt;No Advance&gt;</div>';
				resultRows += sectionRowTemplate.replace("{{label}}", $(this).attr('mcrdtxt'))
								.replace("{{divID}}", controlDivID)
								.replace("{{divClassList}}", controlDivClassList)
								.replace("{{labelfor}}", controlID)
								.replace("{{content}}", rowControls);
			});

			onchangeFunctions = "updateTasksAFExample('roll_adv','roll_adv_afid','divroll_adv_afid');";
			
			rowControls = '<input type="text" name="roll_adv" id="roll_adv" value="" class="form-control form-control-sm dateControl" style="max-width:150px;" onchange="'+ onchangeFunctions +'">\
							<div class="input-group-append"><span class="input-group-text cursor-pointer calendar-button" onclick="$(\'#roll_adv.dateControl\').focus();"><i class="fa-solid fa-calendar"></i></span></div>\
							<button type="button" class="btn btn-pill btn-secondary btn-sm ml-2 py-0" onclick="clearTasksRollADVDate();">clear</button>';
			resultRows += sectionRowTemplate.replace("{{label}}", "When should we next advance these dates?")
							.replace("{{divID}}", 'div_roll_adv')
							.replace("{{divClassList}}", '')
							.replace("{{labelfor}}", "roll_adv")
							.replace("{{content}}", rowControls);
			
			rowControls = '<select class="rollafid form-control form-control-sm" name="roll_adv_afid" id="roll_adv_afid" onchange="'+ onchangeFunctions +'"></select> <div id="divroll_adv_afid" class="d-inline-flex align-items-center ml-2">&lt;No Advance&gt;</div>';
			resultRows += sectionRowTemplate.replace("{{label}}", "How should we advance this date?")
							.replace("{{divID}}", 'div_roll_adv_afid')
							.replace("{{divClassList}}", '')
							.replace("{{labelfor}}", "roll_adv_afid")
							.replace("{{content}}", rowControls);

			var rolltbl = sectionTemplate.replace("{{content}}", resultRows);
			$('div#rolldateYes').html(rolltbl);

			var rollingDates = false;
			var roll_adv = '';
			var roll_afid = '';

			$('input.rolldate').each(function() {
				var mcrdfld = $(this).attr('name');
				roll_afid = '';
				for (var field in strRollDateFields) {
					if (field.toLowerCase() == mcrdfld.toLowerCase()) { roll_afid = strRollDateFields[field]; break; }
				}
				if (roll_afid.length > 0) rollingDates = true;
				tempsel.clone().children().appendTo('#roll_' + mcrdfld + '_afid');
				if (roll_afid.length == 0) $('#roll_' + mcrdfld + '_afid')[0].selectedIndex = 0;
				else $('#roll_' + mcrdfld + '_afid option[value="' + roll_afid + '"]').attr('selected', 'selected'); 
				updateTasksAFExample($(this).attr('id'),'roll_' + mcrdfld + '_afid','divroll_' + mcrdfld + '_afid');
			});

			roll_afid = '';
			for (var field in strRollDateFields) {
				if (field.toLowerCase() == 'afrundate') { roll_afid = strRollDateFields[field].afid; roll_adv = strRollDateFields[field].afrundate; break; }
			}
			if (roll_adv.length > 0 || roll_afid.length > 0) rollingDates = true;
			$('#roll_adv').val(roll_adv);

			tempsel.clone().children().appendTo('#roll_adv_afid');
			if (roll_afid.length == 0) $('#roll_adv_afid')[0].selectedIndex = 0;
			else $('#roll_adv_afid option[value="' + roll_afid + '"]').attr('selected', 'selected'); 
			updateTasksAFExample('roll_adv','roll_adv_afid','divroll_adv_afid');
			mca_setupDatePickerField('roll_adv',moment(new Date()).format('M/D/YYYY'));

			if (rollingDates) {
				$("#rolldate1").prop("checked", true);
				rollTasksDateRdo(1);
			}

			if (typeof strTaskAutomation.automationID != "undefined") {
				toggleScheduleSaveSettings(true);
				$('#mc_taskScheduleRunDate_AFID').trigger('change');
			}

		} else { 
			handleResponseFail();
		}
	};
	var handleResponseFail = function(r) {
		alert('There was a problem loading the advancement formulas. Try again.'); 
	};

	TS_AJX_SYNC('VIRTUALGROUPS','getAdvanceFormula',{ fororg:0 },handleResponse,handleResponseFail,10000,handleResponseFail);
}
function rollTasksDateRdo(v) {
	if (v == 1) $('#rolldateYes').show();
	else {
		$('#rolldateYes').hide();
		$('select.rollafid').val('0').trigger('change');
		$('#roll_adv').val('').trigger('change');
	}
}
function updateTasksAFExample(dt,af,txt) {
	var jtxt = $('#'+txt);
	var dtVal = $('#'+dt).val();

	var chkDateExResult = function(r) {
		if (r.success && r.success.toLowerCase() == 'true') jtxt.html(r.retdate + baseDateTimeString);
		else jtxt.html('Unable to calculate date.');
	};

	if ($('#'+dt).hasClass('rolldate')) {
		var enableRollDateYes = false;
		if (dtVal != '') {
			if (jtxt.closest('.mc_taskfield_afid').hasClass('d-none'))
				jtxt.closest('.mc_taskfield_afid').removeClass('d-none');
			
			enableRollDateYes = true;
		} else {
			$('#'+af).val(0);

			if (!jtxt.closest('.mc_taskfield_afid').hasClass('d-none'))
				jtxt.closest('.mc_taskfield_afid').addClass('d-none');

			var arrRollDateFieldsHavingDates = $('input.rolldate').filter(function(){ return $(this).val().trim().length > 0; });
			if (arrRollDateFieldsHavingDates.length == 0) 
				enableRollDateYes = false;
			else 
				enableRollDateYes = true;
		}

		if (enableRollDateYes) {
			if ($('#rolldate1').prop('disabled'))
				$('#rolldate1').removeAttr('disabled');
			
			if ($('#divautoadvancedatessetting').hasClass('d-none'))
				$('#divautoadvancedatessetting').removeClass('d-none');
		} else {
			if (!$('#rolldate1').prop('disabled'))
				$('#rolldate1').prop('disabled',true);
			if ($('#rolldate1').is(':checked')) {
				$('#rolldate0').prop('checked',true);
				rollTasksDateRdo(0);
			}
			
			if (!$('#divautoadvancedatessetting').hasClass('d-none'))	
				$('#divautoadvancedatessetting').addClass('d-none');
		}
	}

	var afVal = Number($('#'+af).val());

	if (afVal == 0 || dtVal == '') {
		jtxt.html('&lt;No Advance&gt;');
	} else {
		jtxt.html('<i class="fa-light fa-circle-notch fa-spin fa-lg"></i> Calculating...');

		var baseDate = dtVal;
		var baseDateTimeString = '';
		if (baseDate.length > 10) {
			baseDate = moment(dtVal,"M/D/YYYY - H:mm A").format('M/D/YYYY');
			baseDateTimeString = moment(dtVal,"M/D/YYYY - H:mm A").format(' - H:mm A');
		}
		
		var objParams = { baseDate:baseDate, afid:afVal };
		TS_AJX('ADMADVFORM','getAdvanceFormulaDateforAFID',objParams,chkDateExResult,chkDateExResult,10000,chkDateExResult);
	}
}
function clearTasksRollADVDate() {
	$('#roll_adv:enabled').val('');
	updateTasksAFExample('roll_adv','roll_adv_afid','divroll_adv_afid');
}
function massDeleteTasks() {
	if(taskListTable.page.info().recordsTotal == 0){
		alert('There are no filtered entries in '+getTaskFieldLabelPlural()+' to act upon.');
		return false;
	}
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Delete Filtered ' + getTaskFieldLabelPlural(),
		iframe: true,
		contenturl: link_massdeletetasks + '&ntLst=' + ntLst + '&' + $('#frmFilter').serialize(),
		strmodalfooter: {
			classlist: 'd-none'
		}
	});
}